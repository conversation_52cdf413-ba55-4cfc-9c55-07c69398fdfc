import fs from 'fs';
import path from 'path';

/**
 * 创建人类可读的时间戳
 * @returns {string} 格式化的时间戳 YYYY-MM-DD_HH-MM-SS
 */
export function getHumanReadableTimestamp() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day}_${hours}-${minutes}-${seconds}`;
}

/**
 * 确保截图目录存在
 * @param {string} dir - 目录路径
 */
export function ensureScreenshotDirectory(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`创建截图目录: ${dir}`);
  }
}

/**
 * 检查 Cookie 文件是否存在
 * @param {string} cookieFile - Cookie 文件路径
 * @returns {boolean} 文件是否存在
 */
export function checkCookieFile(cookieFile) {
  if (!fs.existsSync(cookieFile)) {
    console.error(`Cookie文件不存在: ${cookieFile}`);
    console.log('请先运行 npm run login 进行登录');
    return false;
  }
  return true;
}

/**
 * 读取并解析 Cookie 文件
 * @param {string} cookieFile - Cookie 文件路径
 * @returns {Array} Cookie 数组
 */
export function loadCookies(cookieFile) {
  try {
    const cookies = JSON.parse(fs.readFileSync(cookieFile, 'utf8'));
    console.log(`已加载 ${cookies.length} 个cookies`);
    return cookies;
  } catch (error) {
    console.error('读取 Cookie 文件失败:', error);
    throw error;
  }
}

/**
 * 保存截图
 * @param {Object} page - Playwright 页面对象
 * @param {string} screenshotDir - 截图目录
 * @param {string} prefix - 文件名前缀
 * @returns {string} 截图文件路径
 */
export async function saveScreenshot(page, screenshotDir, prefix = 'screenshot') {
  ensureScreenshotDirectory(screenshotDir);
  const timestamp = getHumanReadableTimestamp();
  const screenshotPath = path.join(screenshotDir, `${prefix}-${timestamp}.png`);
  await page.screenshot({ path: screenshotPath });
  console.log(`截图已保存: ${screenshotPath}`);
  return screenshotPath;
}
