import config from './config.js';
import { fileURLToPath } from 'url';
import path from 'path';
import { checkCookieFile } from './utils/common-utils.js';
import { createBrowserSession, navigateToWebIDE, executeCommandFlow } from './utils/webide-utils.js';

async function executeCommand() {
  // 检查cookie文件是否存在
  if (!checkCookieFile(config.cookieFile)) {
    return;
  }

  let browser;
  try {
    // 创建浏览器会话
    const { browser: browserInstance, page } = await createBrowserSession(config.cookieFile);
    browser = browserInstance;

    // 导航到WebIDE页面
    await navigateToWebIDE(page);

    // 执行命令流程
    const success = await executeCommandFlow(page, 'screenshot');

    // 保持浏览器打开一段时间以便查看结果
    if (!config.browserOptions.headless) {
      console.log('浏览器将保持打开5秒以便查看结果...');
      await page.waitForTimeout(5000);
    }

  } catch (error) {
    console.error('执行命令过程中发生错误:', error);
  } finally {
    if (browser) {
      await browser.close();
      console.log('浏览器已关闭');
    }
  }
}

// 运行命令执行脚本
const __filename = fileURLToPath(import.meta.url);
const scriptPath = path.resolve(process.argv[1]);

if (path.resolve(__filename) === scriptPath) {
  executeCommand().catch(console.error);
}

export { executeCommand };
