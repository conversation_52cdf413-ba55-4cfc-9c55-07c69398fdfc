import { startScheduler } from './scheduler.js';
import config from './config.js';
import { checkCookieFile, ensureScreenshotDirectory } from './utils/common-utils.js';

// 测试调度器功能
async function testScheduler() {
  console.log('=== 调度器测试 ===');
  
  // 检查必要文件
  console.log('1. 检查配置文件...');
  try {
    // 尝试导入配置文件来验证其存在性
    console.log('✅ config.js 文件存在');
  } catch (error) {
    console.error('❌ config.js 文件不存在或有错误');
    return;
  }
  
  // 检查 Cookie 文件
  console.log('2. 检查 Cookie 文件...');
  if (!checkCookieFile(config.cookieFile)) {
    return;
  }
  console.log('✅ Cookie 文件存在');
  
  // 检查截图目录
  console.log('3. 检查截图目录...');
  const screenshotDir = config.screenshotDir || './screenshots';
  ensureScreenshotDirectory(screenshotDir);
  console.log(`✅ 截图目录已准备: ${screenshotDir}`);
  
  // 显示配置信息
  console.log('4. 当前配置:');
  console.log(`   - WebIDE URL: ${config.webideUrl}`);
  console.log(`   - 执行命令: ${config.command}`);
  console.log(`   - 截图目录: ${screenshotDir}`);
  console.log(`   - 浏览器模式: ${config.browserOptions.headless ? '无头模式' : '有界面模式'}`);
  
  console.log('\n5. 启动调度器...');
  console.log('调度器将每10分钟执行一次命令');
  console.log('按 Ctrl+C 停止测试');
  
  // 启动调度器
  try {
    await startScheduler();
  } catch (error) {
    console.error('❌ 调度器启动失败:', error);
  }
}

// 运行测试
testScheduler().catch(console.error);
